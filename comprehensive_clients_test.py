#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل ومفصل لقسم العملاء
للتأكد من عدم وجود أي مشاكل نهائياً
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt
from database import Session, Client
from ui.clients import ClientsWidget, AddClientDialog, DeleteClientDialog, BaseClientDialog

def test_base_dialog_class():
    """اختبار الكلاس الأساسي BaseClientDialog"""
    print("🧪 اختبار الكلاس الأساسي BaseClientDialog...")
    
    try:
        session = Session()
        
        # اختبار إنشاء الكلاس الأساسي
        base_dialog = BaseClientDialog(session, None, "اختبار")
        
        # التحقق من الخصائص الأساسية
        assert hasattr(base_dialog, 'session'), "خاصية session غير موجودة"
        assert hasattr(base_dialog, 'parent_widget'), "خاصية parent_widget غير موجودة"
        assert hasattr(base_dialog, 'drag_position'), "خاصية drag_position غير موجودة"
        
        # التحقق من الدوال الأساسية
        assert hasattr(base_dialog, 'style_advanced_button'), "دالة style_advanced_button غير موجودة"
        assert hasattr(base_dialog, 'customize_title_bar'), "دالة customize_title_bar غير موجودة"
        assert hasattr(base_dialog, 'mousePressEvent'), "دالة mousePressEvent غير موجودة"
        assert hasattr(base_dialog, 'mouseMoveEvent'), "دالة mouseMoveEvent غير موجودة"
        
        # التحقق من أن الدوال قابلة للاستدعاء
        assert callable(base_dialog.style_advanced_button), "دالة style_advanced_button غير قابلة للاستدعاء"
        assert callable(base_dialog.customize_title_bar), "دالة customize_title_bar غير قابلة للاستدعاء"
        
        print("✅ الكلاس الأساسي BaseClientDialog يعمل بشكل مثالي")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الكلاس الأساسي: {str(e)}")
        return False

def test_clients_widget_initialization():
    """اختبار تهيئة واجهة العملاء"""
    print("🧪 اختبار تهيئة واجهة العملاء...")
    
    try:
        session = Session()
        
        # اختبار إنشاء الواجهة
        clients_widget = ClientsWidget(session)
        
        # التحقق من الخصائص الأساسية
        assert hasattr(clients_widget, 'session'), "خاصية session غير موجودة"
        assert clients_widget.session == session, "session غير محفوظ بشكل صحيح"
        
        # التحقق من وجود الجدول
        assert hasattr(clients_widget, 'clients_table'), "جدول العملاء غير موجود"
        
        # التحقق من وجود الأزرار
        assert hasattr(clients_widget, 'add_button'), "زر الإضافة غير موجود"
        assert hasattr(clients_widget, 'edit_button'), "زر التعديل غير موجود"
        assert hasattr(clients_widget, 'delete_button'), "زر الحذف غير موجود"
        assert hasattr(clients_widget, 'refresh_button'), "زر التحديث غير موجود"
        
        # التحقق من وجود دوال الأزرار
        assert hasattr(clients_widget, 'add_client'), "دالة add_client غير موجودة"
        assert hasattr(clients_widget, 'edit_client'), "دالة edit_client غير موجودة"
        assert hasattr(clients_widget, 'delete_client'), "دالة delete_client غير موجودة"
        assert hasattr(clients_widget, 'refresh_data'), "دالة refresh_data غير موجودة"
        
        print("✅ تهيئة واجهة العملاء تعمل بشكل مثالي")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تهيئة واجهة العملاء: {str(e)}")
        return False

def test_add_client_dialog_inheritance():
    """اختبار وراثة AddClientDialog من BaseClientDialog"""
    print("🧪 اختبار وراثة AddClientDialog...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # اختبار إنشاء نافذة الإضافة
        add_dialog = AddClientDialog(session, clients_widget)
        
        # التحقق من الوراثة
        assert isinstance(add_dialog, BaseClientDialog), "AddClientDialog لا ترث من BaseClientDialog"
        
        # التحقق من الخصائص الموروثة
        assert hasattr(add_dialog, 'session'), "خاصية session غير موروثة"
        assert hasattr(add_dialog, 'parent_widget'), "خاصية parent_widget غير موروثة"
        assert add_dialog.session == session, "session غير محفوظ بشكل صحيح"
        assert add_dialog.parent_widget == clients_widget, "parent_widget غير محفوظ بشكل صحيح"
        
        # التحقق من الدوال الموروثة
        assert hasattr(add_dialog, 'style_advanced_button'), "دالة style_advanced_button غير موروثة"
        assert hasattr(add_dialog, 'customize_title_bar'), "دالة customize_title_bar غير موروثة"
        
        # التحقق من الخصائص الخاصة
        assert hasattr(add_dialog, 'client'), "خاصية client غير موجودة"
        assert hasattr(add_dialog, 'is_edit_mode'), "خاصية is_edit_mode غير موجودة"
        assert add_dialog.is_edit_mode == False, "وضع الإضافة غير صحيح"
        
        print("✅ وراثة AddClientDialog تعمل بشكل مثالي")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في وراثة AddClientDialog: {str(e)}")
        return False

def test_delete_client_dialog_inheritance():
    """اختبار وراثة DeleteClientDialog من BaseClientDialog"""
    print("🧪 اختبار وراثة DeleteClientDialog...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # إنشاء عميل تجريبي
        test_client = Client(name="عميل للاختبار", balance=0)
        session.add(test_client)
        session.commit()
        
        # اختبار إنشاء نافذة الحذف
        delete_dialog = DeleteClientDialog(clients_widget, test_client)
        
        # التحقق من الوراثة
        assert isinstance(delete_dialog, BaseClientDialog), "DeleteClientDialog لا ترث من BaseClientDialog"
        
        # التحقق من الخصائص الموروثة
        assert hasattr(delete_dialog, 'session'), "خاصية session غير موروثة"
        assert hasattr(delete_dialog, 'parent_widget'), "خاصية parent_widget غير موروثة"
        assert delete_dialog.session == session, "session غير محفوظ بشكل صحيح"
        assert delete_dialog.parent_widget == clients_widget, "parent_widget غير محفوظ بشكل صحيح"
        
        # التحقق من الدوال الموروثة
        assert hasattr(delete_dialog, 'style_advanced_button'), "دالة style_advanced_button غير موروثة"
        assert hasattr(delete_dialog, 'customize_title_bar'), "دالة customize_title_bar غير موروثة"
        
        # التحقق من الخصائص الخاصة
        assert hasattr(delete_dialog, 'client'), "خاصية client غير موجودة"
        assert delete_dialog.client == test_client, "بيانات العميل غير محفوظة بشكل صحيح"
        
        # تنظيف
        session.delete(test_client)
        session.commit()
        
        print("✅ وراثة DeleteClientDialog تعمل بشكل مثالي")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في وراثة DeleteClientDialog: {str(e)}")
        return False

def test_edit_mode_functionality():
    """اختبار وضع التعديل في AddClientDialog"""
    print("🧪 اختبار وضع التعديل...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # إنشاء عميل تجريبي
        test_client = Client(
            name="عميل للتعديل",
            phone="01234567890",
            email="<EMAIL>",
            address="عنوان للتعديل",
            balance=500.0,
            notes="ملاحظات للتعديل"
        )
        session.add(test_client)
        session.commit()
        
        # اختبار إنشاء نافذة التعديل
        edit_dialog = AddClientDialog(session, clients_widget, test_client)
        
        # التحقق من وضع التعديل
        assert edit_dialog.is_edit_mode == True, "وضع التعديل غير مفعل"
        assert edit_dialog.client == test_client, "بيانات العميل غير محملة"
        
        # التحقق من العنوان
        assert "تعديل" in edit_dialog.windowTitle(), "عنوان النافذة لا يشير للتعديل"
        
        # تنظيف
        session.delete(test_client)
        session.commit()
        
        print("✅ وضع التعديل يعمل بشكل مثالي")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في وضع التعديل: {str(e)}")
        return False

def test_parameter_passing():
    """اختبار تمرير المعاملات بين الكلاسات"""
    print("🧪 اختبار تمرير المعاملات...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # اختبار تمرير المعاملات لنافذة الإضافة
        add_dialog = AddClientDialog(session, clients_widget)
        assert add_dialog.session is session, "session غير ممرر بشكل صحيح لنافذة الإضافة"
        assert add_dialog.parent_widget is clients_widget, "parent_widget غير ممرر بشكل صحيح لنافذة الإضافة"
        
        # إنشاء عميل تجريبي
        test_client = Client(name="اختبار المعاملات", balance=0)
        session.add(test_client)
        session.commit()
        
        # اختبار تمرير المعاملات لنافذة التعديل
        edit_dialog = AddClientDialog(session, clients_widget, test_client)
        assert edit_dialog.session is session, "session غير ممرر بشكل صحيح لنافذة التعديل"
        assert edit_dialog.parent_widget is clients_widget, "parent_widget غير ممرر بشكل صحيح لنافذة التعديل"
        assert edit_dialog.client is test_client, "client غير ممرر بشكل صحيح لنافذة التعديل"
        
        # اختبار تمرير المعاملات لنافذة الحذف
        delete_dialog = DeleteClientDialog(clients_widget, test_client)
        assert delete_dialog.session is session, "session غير ممرر بشكل صحيح لنافذة الحذف"
        assert delete_dialog.parent_widget is clients_widget, "parent_widget غير ممرر بشكل صحيح لنافذة الحذف"
        assert delete_dialog.client is test_client, "client غير ممرر بشكل صحيح لنافذة الحذف"
        
        # تنظيف
        session.delete(test_client)
        session.commit()
        
        print("✅ تمرير المعاملات يعمل بشكل مثالي")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تمرير المعاملات: {str(e)}")
        return False

def test_no_duplicate_code():
    """اختبار عدم وجود كود مكرر"""
    print("🧪 اختبار عدم وجود كود مكرر...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # اختبار أن AddClientDialog لا تحتوي على دوال مكررة
        add_dialog = AddClientDialog(session, clients_widget)
        
        # التأكد من أن الدوال موروثة وليست مكررة
        assert add_dialog.style_advanced_button.__qualname__.startswith('BaseClientDialog'), "دالة style_advanced_button مكررة في AddClientDialog"
        assert add_dialog.customize_title_bar.__qualname__.startswith('BaseClientDialog'), "دالة customize_title_bar مكررة في AddClientDialog"
        
        # اختبار أن DeleteClientDialog لا تحتوي على دوال مكررة
        test_client = Client(name="اختبار الكود المكرر", balance=0)
        session.add(test_client)
        session.commit()
        
        delete_dialog = DeleteClientDialog(clients_widget, test_client)
        
        # التأكد من أن الدوال موروثة وليست مكررة
        assert delete_dialog.style_advanced_button.__qualname__.startswith('BaseClientDialog'), "دالة style_advanced_button مكررة في DeleteClientDialog"
        assert delete_dialog.customize_title_bar.__qualname__.startswith('BaseClientDialog'), "دالة customize_title_bar مكررة في DeleteClientDialog"
        
        # تنظيف
        session.delete(test_client)
        session.commit()
        
        print("✅ لا يوجد كود مكرر - الكود نظيف ومنظم")
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الكود المكرر: {str(e)}")
        return False

def main():
    """تشغيل جميع الاختبارات الشاملة"""
    print("🚀 بدء الاختبار الشامل لقسم العملاء...")
    print("=" * 60)
    
    app = QApplication(sys.argv)
    
    comprehensive_tests = [
        test_base_dialog_class,
        test_clients_widget_initialization,
        test_add_client_dialog_inheritance,
        test_delete_client_dialog_inheritance,
        test_edit_mode_functionality,
        test_parameter_passing,
        test_no_duplicate_code
    ]
    
    passed = 0
    failed = 0
    
    for test in comprehensive_tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في تشغيل الاختبار: {str(e)}")
            failed += 1
        print("-" * 40)
    
    print("📊 نتائج الاختبار الشامل:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 جميع الاختبارات الشاملة نجحت!")
        print("✨ قسم العملاء خالي من المشاكل تماماً!")
        print("🔥 الكود نظيف ومنظم ويعمل بشكل مثالي!")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
