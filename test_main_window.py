#!/usr/bin/env python3
"""
اختبار سريع للنافذة الرئيسية لحل مشكلة الشاشة السوداء
"""

import sys
import os
import warnings

# إعداد إخفاء التحذيرات
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

def test_main_window():
    """اختبار النافذة الرئيسية"""
    try:
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setStyle("Fusion")
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # استيراد المكونات
        from database import init_db, get_session, User
        from ui.main_window import MainWindow
        
        print("✅ تم استيراد المكونات بنجاح")
        
        # إعداد قاعدة البيانات
        init_db()
        session = get_session()
        
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        
        # إنشاء المستخدم
        user = session.query(User).filter_by(role="admin").first()
        if not user:
            user = User(
                username="admin",
                password="admin", 
                role="admin",
                full_name="المدير العام"
            )
            session.add(user)
            session.commit()
            
        print("✅ تم إعداد المستخدم بنجاح")
        
        # إنشاء النافذة الرئيسية
        print("🚀 إنشاء النافذة الرئيسية...")
        window = MainWindow(session, user)
        
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # إظهار النافذة
        window.show()
        print("✅ تم إظهار النافذة بنجاح")
        
        # تشغيل التطبيق لمدة قصيرة للاختبار
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(3000, app.quit)  # إغلاق بعد 3 ثوان
        
        print("🎉 الاختبار نجح! النافذة تعمل بدون شاشة سوداء")
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_main_window())
