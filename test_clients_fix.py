#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاحات قسم العملاء
تأكد من عمل جميع الوظائف بعد التنظيف والإصلاح
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from database import Session, Client
from ui.clients import ClientsWidget, AddClientDialog, DeleteClientDialog

def test_clients_widget():
    """اختبار واجهة العملاء الرئيسية"""
    print("🧪 اختبار واجهة العملاء الرئيسية...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        print("✅ تم إنشاء واجهة العملاء بنجاح")
        
        # اختبار تحميل البيانات
        clients_widget.refresh_data()
        print("✅ تم تحميل البيانات بنجاح")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة العملاء: {str(e)}")
        return False

def test_add_client_dialog():
    """اختبار نافذة إضافة العميل"""
    print("🧪 اختبار نافذة إضافة العميل...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # اختبار إنشاء نافذة الإضافة
        add_dialog = AddClientDialog(session, clients_widget)
        print("✅ تم إنشاء نافذة إضافة العميل بنجاح")
        
        # اختبار الكلاس الأساسي
        assert hasattr(add_dialog, 'style_advanced_button'), "دالة style_advanced_button غير موجودة"
        assert hasattr(add_dialog, 'customize_title_bar'), "دالة customize_title_bar غير موجودة"
        print("✅ الكلاس الأساسي يعمل بشكل صحيح")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة إضافة العميل: {str(e)}")
        return False

def test_edit_client_dialog():
    """اختبار نافذة تعديل العميل"""
    print("🧪 اختبار نافذة تعديل العميل...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # إنشاء عميل تجريبي للاختبار
        test_client = Client(
            name="عميل تجريبي",
            phone="01234567890",
            email="<EMAIL>",
            address="عنوان تجريبي",
            balance=1000.0,
            notes="ملاحظات تجريبية"
        )
        session.add(test_client)
        session.commit()
        
        # اختبار إنشاء نافذة التعديل
        edit_dialog = AddClientDialog(session, clients_widget, test_client)
        print("✅ تم إنشاء نافذة تعديل العميل بنجاح")
        
        # التأكد من وضع التعديل
        assert edit_dialog.is_edit_mode == True, "وضع التعديل غير مفعل"
        assert edit_dialog.client == test_client, "بيانات العميل غير محملة"
        print("✅ وضع التعديل يعمل بشكل صحيح")
        
        # تنظيف البيانات التجريبية
        session.delete(test_client)
        session.commit()
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة تعديل العميل: {str(e)}")
        return False

def test_delete_client_dialog():
    """اختبار نافذة حذف العميل"""
    print("🧪 اختبار نافذة حذف العميل...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # إنشاء عميل تجريبي للاختبار
        test_client = Client(
            name="عميل للحذف",
            phone="01111111111",
            balance=500.0
        )
        session.add(test_client)
        session.commit()
        
        # اختبار إنشاء نافذة الحذف
        delete_dialog = DeleteClientDialog(clients_widget, test_client)
        print("✅ تم إنشاء نافذة حذف العميل بنجاح")
        
        # التأكد من بيانات العميل
        assert delete_dialog.client == test_client, "بيانات العميل غير محملة"
        print("✅ بيانات العميل محملة بشكل صحيح")
        
        # تنظيف البيانات التجريبية
        session.delete(test_client)
        session.commit()
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة حذف العميل: {str(e)}")
        return False

def test_base_dialog_inheritance():
    """اختبار وراثة الكلاس الأساسي"""
    print("🧪 اختبار وراثة الكلاس الأساسي...")
    
    try:
        session = Session()
        clients_widget = ClientsWidget(session)
        
        # اختبار AddClientDialog
        add_dialog = AddClientDialog(session, clients_widget)
        assert hasattr(add_dialog, 'session'), "session غير موجود في AddClientDialog"
        assert hasattr(add_dialog, 'parent_widget'), "parent_widget غير موجود في AddClientDialog"
        
        # اختبار DeleteClientDialog
        test_client = Client(name="اختبار", balance=0)
        session.add(test_client)
        session.commit()
        
        delete_dialog = DeleteClientDialog(clients_widget, test_client)
        assert hasattr(delete_dialog, 'session'), "session غير موجود في DeleteClientDialog"
        assert hasattr(delete_dialog, 'parent_widget'), "parent_widget غير موجود في DeleteClientDialog"
        
        print("✅ وراثة الكلاس الأساسي تعمل بشكل صحيح")
        
        # تنظيف
        session.delete(test_client)
        session.commit()
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وراثة الكلاس الأساسي: {str(e)}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("🚀 بدء اختبار إصلاحات قسم العملاء...")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    
    tests = [
        test_clients_widget,
        test_add_client_dialog,
        test_edit_client_dialog,
        test_delete_client_dialog,
        test_base_dialog_inheritance
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في تشغيل الاختبار: {str(e)}")
            failed += 1
        print("-" * 30)
    
    print("📊 نتائج الاختبارات:")
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📈 معدل النجاح: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 جميع الاختبارات نجحت! الإصلاحات تعمل بشكل مثالي.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
