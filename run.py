#!/usr/bin/env python3
"""
ملف تشغيل مبسط للبرنامج - حل سريع لمشكلة الشاشة السوداء
استخدم هذا الملف إذا واجهت مشكلة الشاشة السوداء
"""

import sys
import os

# إضافة المسار الحالي
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_program():
    """تشغيل البرنامج بطريقة آمنة"""
    try:
        print("=" * 50)
        print("🚀 تشغيل برنامج المحاسبة الإداري")
        print("=" * 50)
        
        # محاولة تشغيل الملف الآمن أولاً
        try:
            print("📋 محاولة تشغيل الملف الآمن...")
            import main_safe
            main_safe.main()
        except ImportError:
            print("⚠️ الملف الآمن غير متوفر، تشغيل الملف الرئيسي...")
            import main
            main.main()
        except Exception as e:
            print(f"❌ خطأ في الملف الآمن: {e}")
            print("🔄 محاولة تشغيل الملف الرئيسي...")
            import main
            main.main()
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        print("\n💡 نصائح لحل المشكلة:")
        print("1. تأكد من تثبيت PyQt5: pip install PyQt5")
        print("2. تأكد من تثبيت SQLAlchemy: pip install sqlalchemy")
        print("3. تأكد من وجود جميع الملفات في المجلد")
        print("4. جرب تشغيل: python main.py")
        print("5. جرب تشغيل: python main_safe.py")
        
        input("\nاضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    run_program()
