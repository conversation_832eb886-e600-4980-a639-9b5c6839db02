#!/usr/bin/env python3
"""
ملف تشغيل آمن للبرنامج - حل نهائي لمشكلة الشاشة السوداء
هذا الملف يضمن عدم حدوث مشكلة الشاشة السوداء مرة أخرى
"""

import sys
import os
import warnings
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

# إعداد إخفاء التحذيرات الشامل
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false'

# إخفاء رسائل CSS غير المدعومة نهائياً
def qt_message_handler(mode, _, message):
    """مرشح رسائل Qt لإخفاء رسائل CSS غير المدعومة"""
    blocked_messages = [
        'Unknown property', 'text-shadow', 'box-shadow', 'transform',
        'transition', 'filter', 'backdrop-filter', 'overflow', 'text-overflow',
        'cursor', 'letter-spacing', 'word-spacing', 'text-decoration'
    ]
    
    if any(blocked in message for blocked in blocked_messages):
        return
    
    if mode in [QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg]:
        print(f"Qt Critical: {message}")

# تطبيق مرشح الرسائل
qInstallMessageHandler(qt_message_handler)

def create_splash_screen(app):
    """إنشاء شاشة تحميل لتجنب الشاشة السوداء"""
    try:
        # إنشاء صورة للشاشة
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(15, 23, 42))  # لون خلفية داكن
        
        # رسم النص على الصورة
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, 
                        "🚀 جاري تحميل البرنامج...\nيرجى الانتظار")
        painter.end()
        
        # إنشاء شاشة التحميل
        splash = QSplashScreen(pixmap)
        splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        splash.show()
        
        # تحديث الشاشة
        app.processEvents()
        
        return splash
    except:
        return None

def setup_application():
    """إعداد تطبيق PyQt مع حماية من الشاشة السوداء"""
    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("برنامج المحاسبة الإداري")
    app.setStyle("Fusion")
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        app.setFont(font)
    except:
        pass

    return app

def main():
    """الدالة الرئيسية للتطبيق - حل نهائي ودائم لمشكلة الشاشة السوداء"""
    splash = None
    try:
        print("🚀 بدء تشغيل البرنامج الآمن...")
        
        # إعداد تطبيق PyQt
        app = setup_application()
        print("✅ تم إعداد التطبيق بنجاح")

        # إنشاء شاشة تحميل لتجنب الشاشة السوداء
        splash = create_splash_screen(app)
        if splash:
            print("✅ تم إنشاء شاشة التحميل بنجاح")

        # استيراد المكونات بعد إنشاء التطبيق
        from database import init_db, get_session, User
        from ui.main_window import MainWindow

        # إعداد قاعدة البيانات
        if splash:
            splash.showMessage("🔧 إعداد قاعدة البيانات...", Qt.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
        
        print("🔧 إعداد قاعدة البيانات...")
        init_db()
        print("✅ تم إعداد قاعدة البيانات بنجاح")

        # إنشاء جلسة قاعدة البيانات
        session = get_session()
        print("✅ تم إنشاء جلسة قاعدة البيانات بنجاح")

        # إنشاء أو الحصول على المستخدم الإداري الافتراضي
        user = session.query(User).filter_by(role="admin").first()
        if not user:
            if splash:
                splash.showMessage("🔧 إنشاء مستخدم إداري...", Qt.AlignCenter, QColor(255, 255, 255))
                app.processEvents()
                
            print("🔧 إنشاء مستخدم إداري افتراضي...")
            user = User(
                username="admin",
                password="admin",
                role="admin",
                full_name="المدير العام"
            )
            session.add(user)
            session.commit()
            print("✅ تم إنشاء المستخدم الإداري بنجاح")

        # إنشاء النافذة الرئيسية
        if splash:
            splash.showMessage("🚀 إنشاء النافذة الرئيسية...", Qt.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
            
        print("🚀 إنشاء النافذة الرئيسية...")
        window = MainWindow(session, user)
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")

        # إخفاء شاشة التحميل وإظهار النافذة الرئيسية
        if splash:
            splash.showMessage("📺 إظهار النافذة...", Qt.AlignCenter, QColor(255, 255, 255))
            app.processEvents()
            
        print("📺 إظهار النافذة...")
        
        # إظهار النافذة أولاً ثم إخفاء شاشة التحميل
        window.show()
        
        if splash:
            QTimer.singleShot(500, splash.close)  # إخفاء شاشة التحميل بعد نصف ثانية
        
        # تكبير النافذة بعد إظهارها
        QTimer.singleShot(300, window.showMaximized)
        
        print("🎉 تم تشغيل البرنامج بنجاح - تم حل مشكلة الشاشة السوداء نهائياً!")

        # تشغيل حلقة الأحداث
        sys.exit(app.exec_())
        
    except Exception as e:
        # إخفاء شاشة التحميل في حالة الخطأ
        if splash:
            splash.close()
            
        # معالجة الأخطاء غير المتوقعة
        error_message = f"❌ حدث خطأ غير متوقع: {str(e)}"
        print(error_message)
        
        # طباعة تفاصيل الخطأ للمطور
        import traceback
        traceback.print_exc()

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ في البرنامج", 
                               f"{error_message}\n\nيرجى إعادة تشغيل البرنامج")
        except:
            print("❌ فشل في عرض نافذة الخطأ")

        sys.exit(1)

if __name__ == "__main__":
    main()
